import { _decorator, Component, math, Node, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Main')
export class Main extends Component {


    @property
    initialAngle: number = 40; // 初始摆动角度
    @property
    swingDuration: number = 0.8; // 单次摆动持续时间
    @property
    decelerationRate: number = 0.9; // 减速系数 (0-1)
    @property
    minAngleThreshold: number = 1; // 停止角度阈值

    private currentTween: any = null;
    private currentAngle: number = 0;
    private isSwinging: boolean = false;

    @property(Node)
    private _point: Node = null;

    start() {

        this.startSwing();
    }



    startSwing() {
        if (this.isSwinging) return;

        this.isSwinging = true;
        this.currentAngle = this.initialAngle;
        this.swingTo(-this.currentAngle);
    }

    swingTo(targetAngle: number) {
        // 计算当前速度下的持续时间（随着摆动逐渐减小）
        const duration = this.swingDuration * (this.currentAngle / this.initialAngle);

        this.currentTween = tween(this.node)
            .to(duration, { angle: targetAngle }, {
                onUpdate: (target, ratio) => {
                    this.currentAngle = math.lerp(-targetAngle, targetAngle, ratio);
                }
            })
            .call(() => {
                // 应用减速
                this.currentAngle *= this.decelerationRate;

                // 检查是否停止
                if (Math.abs(this.currentAngle) < this.minAngleThreshold) {
                    this.stopSwing();
                } else {
                    // 继续反向摆动
                    this.swingTo(this.currentAngle);
                }
            })
            .start();
    }

    stopSwing() {
        if (this.currentTween) {
            this.currentTween.stop();
            this.currentTween = null;
        }
        this.isSwinging = false;

        // 最终微调归零（可选）
        tween(this.node)
            .to(0.2, { angle: 0 })
            .start();
    }

    // 外部调用添加额外惯性
    addInertia(extraForce: number) {
        if (!this.isSwinging) {
            this.startSwing();
        }

        // 在当前摆动方向上增加力
        const direction = this.node.angle > 0 ? 1 : -1;
        this.currentAngle += extraForce * direction;
        this.currentAngle = math.clamp(this.currentAngle, -this.initialAngle * 1.5, this.initialAngle * 1.5);

        // 重新计算摆动
        if (this.currentTween) {
            this.currentTween.stop();
        }
        this.swingTo(-this.currentAngle);
    }

    update(deltaTime: number) {

    }
}


