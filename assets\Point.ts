import { _decorator, Component, math, Node, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Point')
export class Point extends Component {


    @property
    initialAngle: number = 40; // 初始摆动角度
    @property
    swingDuration: number = 0.8; // 单次摆动持续时间
    @property
    decelerationRate: number = 0.9; // 减速系数 (0-1)
    @property
    minAngleThreshold: number = 1; // 停止角度阈值

    private currentTween: any = null;
    private currentAngle: number = 0;
    private isSwinging: boolean = false;

    @property(Node)
    private _point: Node = null;

    start() {

        this.startSwing();
    }



    startSwing() {
        if (this.isSwinging) return;

        this.isSwinging = true;
        this.currentAngle = this.initialAngle; // 从左边40度开始

        // 设置初始位置到左边40度
        this.node.angle = this.currentAngle;

        // 开始向右摆动（负角度）
        this.swingTo(-this.currentAngle);
    }

    swingTo(targetAngle: number) {
        // 计算当前摆动幅度对应的持续时间
        const currentAmplitude = Math.abs(this.currentAngle);
        const duration = this.swingDuration * (currentAmplitude / this.initialAngle);

        this.currentTween = tween(this.node)
            .to(duration, { angle: targetAngle })
            .call(() => {
                // 应用减速，减小摆动幅度
                this.currentAngle *= this.decelerationRate;

                // 检查是否停止摆动
                if (Math.abs(this.currentAngle) < this.minAngleThreshold) {
                    this.stopSwing();
                } else {
                    // 继续反向摆动
                    this.swingTo(this.currentAngle);
                }
            })
            .start();
    }

    stopSwing() {
        if (this.currentTween) {
            this.currentTween.stop();
            this.currentTween = null;
        }
        this.isSwinging = false;

        // 最终微调归零（可选）
        tween(this.node)
            .to(0.2, { angle: 0 })
            .start();
    }

    // 外部调用添加额外惯性
    addInertia(extraForce: number) {
        if (!this.isSwinging) {
            this.startSwing();
            return;
        }

        // 增加摆动幅度
        this.currentAngle = Math.abs(this.currentAngle) + extraForce;
        this.currentAngle = math.clamp(this.currentAngle, 0, this.initialAngle * 1.5);

        // 重新计算摆动，保持当前方向
        if (this.currentTween) {
            this.currentTween.stop();
        }

        // 根据当前角度决定下一个摆动方向
        const nextTarget = this.node.angle > 0 ? -this.currentAngle : this.currentAngle;
        this.swingTo(nextTarget);
    }

    // update(deltaTime: number) {
    //     // 如果需要在每帧更新逻辑，可以在这里添加
    // }
}


